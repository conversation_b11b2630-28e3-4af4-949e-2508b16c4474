import React, { useState } from 'react';
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft, X, Heart, Share2, Check } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { formatCurrency } from '../../utils/helpers';
import { useNotification } from '../../contexts/NotificationContext';
import { orderService, CreateOrderRequest } from '../../services/orderService';
import { messageService } from '../../services/messageService';
import { useAuth } from '../../contexts/AuthContext';
import PaymentModal from '../payment/PaymentModal';

interface CartModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CartModal: React.FC<CartModalProps> = ({ isOpen, onClose }) => {
  const { items, totalItems, updateQuantity, removeFromCart, clearCart } = useCart();
  const { addToast } = useNotification();
  const { user } = useAuth();
  const [selectAll, setSelectAll] = useState(true);
  const [selectedItems, setSelectedItems] = useState<string[]>(() => items.map(item => item.id));
  const [selectedPayment, setSelectedPayment] = useState<string>('cash_on_delivery');
  const [showShippingForm, setShowShippingForm] = useState(false);
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);
  const [shippingAddress, setShippingAddress] = useState({
    street: '',
    city: '',
    state: '',
    country: 'Malawi',
    postalCode: ''
  });
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState<string>('');
  const [currentOrderAmount, setCurrentOrderAmount] = useState<number>(0);

  // Group items by designer with proper error handling
  const itemsByDesigner = items.reduce((acc, item) => {
    // Handle cases where designer might be undefined or null
    const designer = item.product?.designer;
    const designerId = designer?._id || 'unknown';
    const designerName = designer?.name || 'Unknown Designer';

    if (!acc[designerId]) {
      acc[designerId] = {
        name: designerName,
        items: []
      };
    }
    acc[designerId].items.push(item);
    return acc;
  }, {} as Record<string, { name: string; items: typeof items }>);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
      return;
    }

    // Find the cart item to get product and size info
    const cartItem = items.find(item => item.id === itemId);
    if (!cartItem) return;

    // Get maximum available stock
    let maxStock = cartItem.product.stockQuantity || 1;
    if (cartItem.size && (cartItem.product as any).sizeStock) {
      const sizeStockItem = (cartItem.product as any).sizeStock.find((item: any) => item.size === cartItem.size);
      maxStock = sizeStockItem?.quantity || 0;
    }

    // Validate quantity doesn't exceed available stock
    const validQuantity = Math.min(newQuantity, maxStock);

    if (validQuantity !== newQuantity) {
      addToast('warning', `Only ${maxStock} items available${cartItem.size ? ` in size ${cartItem.size}` : ''}`);
    }

    updateQuantity(itemId, validQuantity);
  };

  const handleRemoveItem = (itemId: string, productName: string) => {
    removeFromCart(itemId);
    addToast('success', `${productName} removed from cart`);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems(items.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
    setSelectAll(!selectAll);
  };

  const handleSelectItem = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const selectedItemsData = items.filter(item => selectedItems.includes(item.id));
  const selectedTotal = selectedItemsData.reduce((sum, item) => {
    const basePrice = (item.product?.discountPrice || item.product?.price || 0) * item.quantity;
    const deliveryTimePrice = item.isCustomOrder && item.customOrderData?.deliveryTimePrice ? item.customOrderData.deliveryTimePrice : 0;
    return sum + basePrice + deliveryTimePrice;
  }, 0);
  const itemsDiscount = selectedItemsData.reduce((sum, item) => {
    if (item.product?.discountPrice && item.product?.price) {
      return sum + ((item.product.price - item.product.discountPrice) * item.quantity);
    }
    return sum;
  }, 0);
  const shippingCost = selectedTotal > 150000 ? 0 : 15000;
  const extraDiscounts = 0; // Can be calculated based on promotions
  const finalTotal = selectedTotal + shippingCost - extraDiscounts;

  // Handle checkout process
  const handleCheckout = async () => {
    if (selectedItems.length === 0) {
      addToast('error', 'Please select items to checkout');
      return;
    }

    if (!user) {
      addToast('error', 'Please login to place an order');
      return;
    }

    setShowShippingForm(true);
  };

  const handlePlaceOrder = async () => {
    if (!shippingAddress.street || !shippingAddress.city) {
      addToast('error', 'Please fill in all shipping address fields');
      return;
    }

    try {
      setIsProcessingOrder(true);

      // Group selected items by designer
      const ordersByDesigner = selectedItemsData.reduce((acc, item) => {
        const designerId = item.product?.designer?._id || 'unknown';
        if (!acc[designerId]) {
          acc[designerId] = [];
        }
        acc[designerId].push(item);
        return acc;
      }, {} as Record<string, typeof selectedItemsData>);

      // Create separate orders for each designer
      const orderPromises = Object.entries(ordersByDesigner).map(async ([designerId, designerItems]) => {
        const designerTotal = designerItems.reduce((sum, item) => {
          const price = item.product?.discountPrice || item.product?.price || 0;
          return sum + (price * item.quantity);
        }, 0);

        // Separate regular products from custom orders
        const regularItems = designerItems.filter(item => !item.isCustomOrder);
        const customOrderItems = designerItems.filter(item => item.isCustomOrder);

        // If this group has only custom orders, create a custom order instead of regular order
        if (regularItems.length === 0 && customOrderItems.length > 0) {
          // For custom orders, we'll handle them differently - just send messages and skip order creation
          console.log('Skipping order creation for custom-only items, will handle via messages');
          return Promise.resolve({
            _id: `custom-order-${Date.now()}`,
            totalAmount: designerTotal,
            items: customOrderItems.map(item => ({
              name: item.product.name,
              price: item.product.price,
              quantity: item.quantity
            }))
          });
        }

        // Create order only for regular products
        const orderData: CreateOrderRequest = {
          items: regularItems.map(item => ({
            product: item.product._id,
            name: item.product.name,
            price: item.product.discountPrice || item.product.price,
            quantity: item.quantity,
            color: item.color,
            size: item.size,
            image: item.product.images?.[0]
          })),
          shippingAddress,
          paymentMethod: 'cash_on_delivery', // Default to COD, will be updated after payment
          notes: `Order placed via cart checkout. Designer: ${designerId}. Total: ${formatCurrency(designerTotal)}`,
          isCustomOrder: false
        };

        return orderService.createOrder(orderData);
      });

      // Wait for all orders to be created
      const createdOrders = await Promise.all(orderPromises);

      // Filter out custom-only orders (they return mock objects)
      const realOrders = createdOrders.filter(order => !order._id.startsWith('custom-order-'));
      const customOnlyOrders = createdOrders.filter(order => order._id.startsWith('custom-order-'));

      // If only one real order and payment method is not COD, show payment modal
      if (realOrders.length === 1 && ['card', 'airtel'].includes(selectedPayment)) {
        const order = realOrders[0];
        setCurrentOrderId(order._id);
        setCurrentOrderAmount(order.totalAmount);
        setShowPaymentModal(true);
        setShowShippingForm(false);
      } else {
        // Multiple orders or COD - complete checkout

        // For COD orders, send custom order messages immediately
        if (selectedPayment === 'cash_on_delivery') {
          await sendCustomOrderMessages();
        }

        clearCart();

        // Create appropriate success message
        let successMessage = '';
        if (realOrders.length > 0 && customOnlyOrders.length > 0) {
          successMessage = `${realOrders.length} order(s) and ${customOnlyOrders.length} custom order(s) placed successfully!`;
        } else if (realOrders.length > 0) {
          successMessage = `${realOrders.length} order(s) placed successfully!`;
        } else if (customOnlyOrders.length > 0) {
          successMessage = `${customOnlyOrders.length} custom order(s) placed successfully!`;
        }

        successMessage += ` ${
          selectedPayment === 'cash_on_delivery'
            ? 'You can pay when your order is delivered.'
            : 'Designers will review and approve your orders.'
        }`;

        addToast('success', successMessage);

        // Close modal and reset forms
        onClose();
        setShowShippingForm(false);
        resetForm();
      }

    } catch (error) {
      console.error('Error placing order:', error);
      addToast('error', 'Failed to place order. Please try again.');
    } finally {
      setIsProcessingOrder(false);
    }
  };

  const resetForm = () => {
    setShippingAddress({
      street: '',
      city: '',
      state: '',
      country: 'Malawi',
      postalCode: ''
    });
    setSelectedPayment('cash_on_delivery');
    setShowPaymentModal(false);
    setCurrentOrderId('');
    setCurrentOrderAmount(0);
  };

  // Function to send custom order messages to designers
  const sendCustomOrderMessages = async () => {
    const customOrderItems = items.filter(item => item.isCustomOrder);

    if (customOrderItems.length > 0) {
      try {
        for (const item of customOrderItems) {
          // Create detailed message for designer
          const measurementsText = item.customOrderData?.measurements
            ? Object.entries(item.customOrderData.measurements)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n')
            : 'No measurements provided';

          const orderMessage = `🎉 **CUSTOM ORDER CONFIRMED**

┌─────────────────────────────────────┐
│              ORDER DETAILS              │
└─────────────────────────────────────┘

👕 **Product:** ${item.product.name}
🎨 **Color:** ${item.color || 'Not specified'}
💰 **Base Price:** MWK ${(item.product.price * item.quantity).toLocaleString()}
${item.customOrderData?.deliveryTimePrice && item.customOrderData.deliveryTimePrice > 0 ? `🚚 **Delivery Time Cost:** MWK ${item.customOrderData.deliveryTimePrice.toLocaleString()}` : ''}
💰 **Total Amount:** MWK ${((item.product.price * item.quantity) + (item.customOrderData?.deliveryTimePrice || 0)).toLocaleString()}

📏 **Measurements:**
${measurementsText}

📝 **Additional Notes:**
${item.customOrderData?.additionalNotes || 'None provided'}

📍 **Delivery Location:**
${item.customOrderData?.deliveryLocation || 'Not specified'}

┌─────────────────────────────────────┐
│                MESSAGE                │
└─────────────────────────────────────┘

Hello! 👋

Great news! A new custom order has been placed and confirmed.

**Next Steps:**
🔹 Please confirm the specifications above
🔹 Provide your estimated completion timeline
🔹 Share any questions about the requirements
🔹 Begin production when ready

The customer is excited to work with you on this custom piece! ✨

Order Status: ✅ **CONFIRMED**`;

          // Send message to designer
          await messageService.startConversationWithDesigner({
            designerId: item.product.designer._id,
            initialMessage: orderMessage,
            productId: item.product._id,
            productName: item.product.name,
            productPrice: item.product.price,
            productImage: item.product.images?.[0]
          });
        }

        console.log(`✅ Custom order messages sent to ${customOrderItems.length} designer(s)`);
      } catch (error) {
        console.error('Error sending custom order messages:', error);
        addToast('warning', 'Order placed, but failed to notify some designers. Please contact them directly.');
      }
    }
  };

  const handlePaymentSuccess = async (paymentData: any) => {
    console.log('Payment successful:', paymentData);

    // Send messages to designers for custom orders
    await sendCustomOrderMessages();

    clearCart();
    addToast('success', 'Payment completed successfully! Your order has been confirmed.');
    onClose();
    resetForm();
  };

  const handlePaymentError = (error: string) => {
    addToast('error', `Payment failed: ${error}`);
    // Keep the order but mark payment as failed
    // User can try payment again from orders page
  };

  // Update selected items when cart items change
  React.useEffect(() => {
    if (items && items.length > 0) {
      setSelectedItems(items.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  }, [items]);

  // Close modal on escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal container */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white shadow-2xl transform transition-all w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col shadow-yellow-400/20">
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-3 border-b-2 border-yellow-400 bg-black text-white flex-shrink-0">
            <div className="flex items-center space-x-3">
              <h1 className="text-lg font-bold text-white">Cart ({totalItems})</h1>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  title="Select all items"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="w-3 h-3 text-yellow-400 border-gray-300 rounded focus:ring-yellow-500"
                />
                <span className="text-xs text-gray-300">Select all items</span>
                <button
                  type="button"
                  title="Delete selected items"
                  className="text-yellow-400 hover:text-yellow-300 text-xs font-medium"
                >
                  Delete selected items
                </button>
              </div>
            </div>
            <button
              type="button"
              title="Close cart"
              onClick={onClose}
              className="rounded-md text-gray-300 hover:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <h2 className="text-base font-bold text-gray-900 mb-2">Your cart is empty</h2>
                <p className="text-gray-500 mb-4 text-xs">Start shopping to add items to your cart</p>
                <button
                  type="button"
                  onClick={onClose}
                  className="inline-flex items-center px-3 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors text-xs"
                >
                  <ArrowLeft className="mr-1 h-3 w-3" />
                  Continue Shopping
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 h-full">
                {/* Cart Items - Scrollable */}
                <div className="lg:col-span-3 overflow-y-auto max-h-[calc(95vh-120px)] p-4 space-y-3">
                  {/* Group items by designer */}
                  {items.length > 0 && Object.entries(itemsByDesigner).map(([designerId, designerData]) => (
                    <div key={designerId} className="border border-gray-200 rounded-md overflow-hidden mb-3">
                      {/* Designer Header */}
                      <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            title={`Select all items from ${designerData.name}`}
                            className="w-3 h-3 text-red-600 border-gray-300 rounded focus:ring-red-500"
                          />
                          <span className="font-medium text-gray-900 text-sm">{designerData.name}</span>
                        </div>
                      </div>

                      {/* Designer Items */}
                      <div className="divide-y divide-gray-200">
                        {designerData.items.map((item) => (
                          <div key={item.id} className="p-3">
                            <div className="flex items-start space-x-3">
                              {/* Checkbox */}
                              <input
                                type="checkbox"
                                title={`Select ${item.product.name}`}
                                checked={selectedItems.includes(item.id)}
                                onChange={() => handleSelectItem(item.id)}
                                className="w-3 h-3 text-red-600 border-gray-300 rounded focus:ring-red-500 mt-1"
                              />

                              {/* Product Image */}
                              <div className="flex-shrink-0">
                                <img
                                  src={item.product.images?.[0] || '/placeholder-image.svg'}
                                  alt={item.product.name}
                                  className="w-16 h-16 object-cover rounded-md"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = '/placeholder-image.svg';
                                  }}
                                />
                              </div>

                              {/* Product Details */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    {item.product.discountPrice && (
                                      <span className="inline-block bg-red-100 text-red-800 text-xs px-1.5 py-0.5 rounded mb-1">
                                        Savings
                                      </span>
                                    )}
                                    <h3 className="text-xs font-medium text-gray-900 mb-1 line-clamp-2">
                                      {item.product.name}
                                      {item.isCustomOrder && (
                                        <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                          Custom
                                        </span>
                                      )}
                                    </h3>
                                    <div className="flex items-center space-x-2 mb-1">
                                      {item.color && (
                                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                                          {item.color}
                                        </span>
                                      )}
                                      {item.size && (
                                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                                          Size: {item.size}
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex items-center space-x-1 mb-1">
                                      <span className="text-sm font-bold text-red-600">
                                        {formatCurrency(item.product.discountPrice || item.product.price)}
                                      </span>
                                      {item.product.discountPrice && (
                                        <span className="text-xs text-gray-500 line-through">
                                          {formatCurrency(item.product.price)}
                                        </span>
                                      )}
                                    </div>
                                    {item.isCustomOrder && item.customOrderData?.deliveryTimePrice && item.customOrderData.deliveryTimePrice > 0 && (
                                      <div className="text-xs text-gray-600 mb-1">
                                        <span className="font-medium">Delivery Time:</span> +{formatCurrency(item.customOrderData.deliveryTimePrice)}
                                      </div>
                                    )}
                                    {item.product.discountPrice && (
                                      <p className="text-xs text-green-600 mb-1">
                                        Save {formatCurrency(item.product.price - item.product.discountPrice)}
                                      </p>
                                    )}
                                    <p className="text-xs text-gray-500">
                                      {item.isCustomOrder ? 'Custom order pricing' : 'Free shipping'}
                                    </p>
                                  </div>

                                  {/* Actions */}
                                  <div className="flex items-center space-x-1 ml-2">
                                    <button
                                      type="button"
                                      title="Add to wishlist"
                                      className="p-1 text-gray-400 hover:text-gray-600"
                                    >
                                      <Heart className="h-3 w-3" />
                                    </button>
                                    <button
                                      type="button"
                                      title="Share product"
                                      className="p-1 text-gray-400 hover:text-gray-600"
                                    >
                                      <Share2 className="h-3 w-3" />
                                    </button>
                                    <button
                                      type="button"
                                      title="Remove from cart"
                                      onClick={() => handleRemoveItem(item.id, item.product.name)}
                                      className="p-1 text-gray-400 hover:text-red-600"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </button>
                                  </div>
                                </div>

                                {/* Quantity Controls */}
                                <div className="flex items-center justify-between mt-2">
                                  <div className="flex items-center border border-gray-300 rounded text-xs">
                                    <button
                                      type="button"
                                      title="Decrease quantity"
                                      onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                      className="px-2 py-1 hover:bg-gray-50 transition-colors text-gray-600"
                                    >
                                      <Minus className="h-3 w-3" />
                                    </button>
                                    <span className="px-3 py-1 text-center min-w-[2rem] text-xs border-x border-gray-300">
                                      {item.quantity}
                                    </span>
                                    <button
                                      type="button"
                                      title="Increase quantity"
                                      onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                      disabled={(() => {
                                        let maxStock = item.product.stockQuantity || 1;
                                        if (item.size && (item.product as any).sizeStock) {
                                          const sizeStockItem = (item.product as any).sizeStock.find((stockItem: any) => stockItem.size === item.size);
                                          maxStock = sizeStockItem?.quantity || 0;
                                        }
                                        return item.quantity >= maxStock;
                                      })()}
                                      className="px-2 py-1 hover:bg-gray-50 transition-colors text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                      <Plus className="h-3 w-3" />
                                    </button>
                                  </div>
                                  {!item.product.inStock && (
                                    <span className="text-xs text-orange-600">Almost sold out</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Summary */}
                <div className="lg:col-span-2 border-l border-gray-200">
                  <div className="bg-white p-4 h-full flex flex-col">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Summary</h2>

                    {/* Product thumbnails */}
                    <div className="flex space-x-1 mb-4 overflow-x-auto">
                      {selectedItemsData.slice(0, 5).map((item) => (
                        <img
                          key={item.id}
                          src={item.product.images?.[0] || '/placeholder-image.svg'}
                          alt={item.product.name}
                          className="w-8 h-8 object-cover rounded flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-image.svg';
                          }}
                        />
                      ))}
                      {selectedItemsData.length > 5 && (
                        <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center text-xs text-gray-500 flex-shrink-0">
                          +{selectedItemsData.length - 5}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Items total</span>
                        <span className="font-medium">{formatCurrency(selectedTotal + itemsDiscount)}</span>
                      </div>

                      {itemsDiscount > 0 && (
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Items discount</span>
                          <span className="text-red-600">-{formatCurrency(itemsDiscount)}</span>
                        </div>
                      )}

                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Subtotal</span>
                        <span className="font-medium">{formatCurrency(selectedTotal)}</span>
                      </div>

                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600">Shipping</span>
                        <span className="font-medium">{formatCurrency(shippingCost)}</span>
                      </div>

                      {extraDiscounts > 0 && (
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Extra discounts</span>
                          <span className="text-red-600">-{formatCurrency(extraDiscounts)}</span>
                        </div>
                      )}

                      <div className="border-t border-gray-200 pt-2">
                        <div className="flex justify-between text-sm font-bold">
                          <span>Estimated total</span>
                          <span>{formatCurrency(finalTotal)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Methods */}
                    <div className="mb-3">
                      <h3 className="text-xs font-medium text-gray-900 mb-2">Payment Method</h3>
                      <div className="space-y-1.5 bg-gray-50 p-2 rounded-md">
                        <label className="flex items-center space-x-2 cursor-pointer hover:bg-white p-1.5 rounded transition-colors">
                          <input
                            type="radio"
                            name="payment"
                            value="card"
                            checked={selectedPayment === 'card'}
                            onChange={(e) => setSelectedPayment(e.target.value)}
                            className="w-3 h-3 text-blue-600"
                          />
                          <div className="flex items-center space-x-2">
                            <div className="w-7 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">
                              CARD
                            </div>
                            <span className="text-xs text-gray-700">Credit/Debit Card</span>
                          </div>
                        </label>
                        <label className="flex items-center space-x-2 cursor-pointer hover:bg-white p-1.5 rounded transition-colors">
                          <input
                            type="radio"
                            name="payment"
                            value="airtel"
                            checked={selectedPayment === 'airtel'}
                            onChange={(e) => setSelectedPayment(e.target.value)}
                            className="w-3 h-3 text-red-600"
                          />
                          <div className="flex items-center space-x-2">
                            <div className="w-10 h-4 bg-red-600 rounded text-white text-xs flex items-center justify-center font-bold">
                              Airtel
                            </div>
                            <span className="text-xs text-gray-700">Airtel Money</span>
                          </div>
                        </label>
                        <label className="flex items-center space-x-2 cursor-pointer hover:bg-white p-1.5 rounded transition-colors">
                          <input
                            type="radio"
                            name="payment"
                            value="cash_on_delivery"
                            checked={selectedPayment === 'cash_on_delivery'}
                            onChange={(e) => setSelectedPayment(e.target.value)}
                            className="w-3 h-3 text-green-600"
                          />
                          <div className="flex items-center space-x-2">
                            <div className="w-7 h-4 bg-green-600 rounded text-white text-xs flex items-center justify-center font-bold">
                              COD
                            </div>
                            <span className="text-xs text-gray-700">Cash on Delivery</span>
                          </div>
                        </label>
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={handleCheckout}
                      disabled={selectedItems.length === 0 || isProcessingOrder}
                      className="w-full bg-red-600 text-white py-2 px-3 rounded-md font-medium hover:bg-red-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed mb-3 text-xs"
                    >
                      <div className="flex flex-col items-center">
                        <span>
                          {isProcessingOrder ? 'Processing...' : `Checkout (${selectedItems.length})`}
                        </span>
                        <span className="text-xs opacity-90">Almost sold out!</span>
                      </div>
                    </button>

                    {/* Buyer Protection */}
                    <div className="bg-gray-50 rounded-md p-2 mt-auto">
                      <h3 className="text-xs font-medium text-gray-900 mb-1">Buyer protection</h3>
                      <div className="flex items-start space-x-1">
                        <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                        <p className="text-xs text-gray-600 leading-tight">
                          Get a full refund if the item is not as described or not delivered
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Shipping Address Modal */}
      {showShippingForm && (
        <div className="fixed inset-0 z-60 overflow-y-auto">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowShippingForm(false)} />
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative bg-white shadow-xl transform transition-all w-full max-w-md rounded-lg overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                <h2 className="text-lg font-bold text-gray-900">Shipping Address</h2>
                <button
                  type="button"
                  title="Close shipping form"
                  onClick={() => setShowShippingForm(false)}
                  className="rounded-md text-gray-400 hover:text-gray-500"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="p-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    value={shippingAddress.street}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, street: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder="Enter your street address"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City *
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.city}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      placeholder="City"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State/Region
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.state}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, state: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      placeholder="State/Region"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Country
                    </label>
                    <select
                      title="Select country"
                      value={shippingAddress.country}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, country: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    >
                      <option value="Malawi">Malawi</option>
                      <option value="Zambia">Zambia</option>
                      <option value="Zimbabwe">Zimbabwe</option>
                      <option value="South Africa">South Africa</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      value={shippingAddress.postalCode}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, postalCode: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      placeholder="Postal Code"
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Order Summary</h3>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Items ({selectedItems.length})</span>
                      <span>{formatCurrency(selectedTotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shipping</span>
                      <span>{formatCurrency(shippingCost)}</span>
                    </div>
                    <div className="flex justify-between font-medium border-t pt-1">
                      <span>Total</span>
                      <span>{formatCurrency(finalTotal)}</span>
                    </div>
                    <div className="flex justify-between text-gray-600">
                      <span>Payment Method</span>
                      <span className="capitalize">{selectedPayment}</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3 pt-2">
                  <button
                    type="button"
                    onClick={() => setShowShippingForm(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handlePlaceOrder}
                    disabled={isProcessingOrder || !shippingAddress.street || !shippingAddress.city}
                    className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
                  >
                    {isProcessingOrder ? 'Placing Order...' : 'Place Order'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        orderId={currentOrderId}
        amount={currentOrderAmount}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </div>
  );
};

export default CartModal;

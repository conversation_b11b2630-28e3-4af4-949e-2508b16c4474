import mongoose from 'mongoose';
import { Product } from '../src/models/product.model.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const updateDeliveryOptions = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');

    // Find all products that don't have price field in delivery options
    const products = await Product.find({
      $or: [
        { 'deliveryOptions.standard.price': { $exists: false } },
        { 'deliveryOptions.express.price': { $exists: false } },
        { 'deliveryOptions.premium.price': { $exists: false } }
      ]
    });

    console.log(`Found ${products.length} products to update`);

    for (const product of products) {
      const updateData = {};

      // Add price field to delivery options if missing
      if (!product.deliveryOptions?.standard?.price && product.deliveryOptions?.standard) {
        updateData['deliveryOptions.standard.price'] = 0;
      }
      if (!product.deliveryOptions?.express?.price && product.deliveryOptions?.express) {
        updateData['deliveryOptions.express.price'] = 0;
      }
      if (!product.deliveryOptions?.premium?.price && product.deliveryOptions?.premium) {
        updateData['deliveryOptions.premium.price'] = 0;
      }

      if (Object.keys(updateData).length > 0) {
        await Product.findByIdAndUpdate(product._id, { $set: updateData });
        console.log(`Updated product: ${product.name}`);
      }
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the migration
updateDeliveryOptions();

import React, { useState, useEffect } from 'react';
import { X, Package, User, Calendar, MapPin, Palette, Ruler, AlertCircle, ShoppingCart } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { useCart } from '../../contexts/CartContext';
import { validateMeasurement, formatMeasurement, getMeasurementInfo } from '../../utils/measurementValidation';

interface Product {
  _id: string;
  name: string;
  images: string[];
  designer: {
    _id: string;
    name: string;
    businessAddress?: string;
  };
  category: string;
  price: number;
  description?: string;
  tags?: string[];
  colors?: string[];
  sizes?: string[];
  materials?: string[];
  inStock?: boolean;
  rating?: number;
  reviewCount?: number;
  discountPrice?: number;
  customizable?: boolean;
  stockQuantity?: number;
  featured?: boolean;
  createdAt?: string;
  updatedAt?: string;
  deliveryTimeOptions?: {
    standard: {
      enabled: boolean;
      days: number;
      description: string;
      price: number;
    };
    express: {
      enabled: boolean;
      days: number;
      description: string;
      price: number;
    };
    premium: {
      enabled: boolean;
      days: number;
      description: string;
      price: number;
    };
  };
}

interface ProductCustomOrderProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
}

interface MeasurementFields {
  [key: string]: string;
}

const ProductCustomOrder: React.FC<ProductCustomOrderProps> = ({ isOpen, onClose, product }) => {
  const { user } = useAuth();
  const { addToast } = useNotification();
  const { addCustomOrderToCart } = useCart();

  const [formData, setFormData] = useState({
    color: '',
    deliveryType: 'standard', // 'standard', 'express', 'premium'
    collectionMethod: 'delivery', // 'delivery' or 'pickup'
    deliveryLocation: '',
    additionalNotes: '',
    measurements: {} as MeasurementFields
  });

  const [loading, setLoading] = useState(false);
  const [measurementErrors, setMeasurementErrors] = useState<Record<string, string>>({});
  const [measurementWarnings, setMeasurementWarnings] = useState<Record<string, string>>({});
  const [productDeliveryOptions, setProductDeliveryOptions] = useState<any>(null);

  // Get measurement fields based on product category
  const getMeasurementFields = (category: string): { field: string; label: string; placeholder: string }[] => {
    const categoryLower = category.toLowerCase();

    const measurementMap: { [key: string]: { field: string; label: string; placeholder: string }[] } = {
      dress: [
        { field: 'bust', label: 'Bust', placeholder: 'e.g., 36 inches' },
        { field: 'waist', label: 'Waist', placeholder: 'e.g., 28 inches' },
        { field: 'hip', label: 'Hip', placeholder: 'e.g., 38 inches' },
        { field: 'shoulder', label: 'Shoulder Width', placeholder: 'e.g., 15 inches' },
        { field: 'dress_length', label: 'Dress Length', placeholder: 'e.g., 42 inches' },
        { field: 'sleeve', label: 'Sleeve Length', placeholder: 'e.g., 24 inches' }
      ],
      suit: [
        { field: 'chest', label: 'Chest', placeholder: 'e.g., 40 inches' },
        { field: 'waist', label: 'Waist', placeholder: 'e.g., 32 inches' },
        { field: 'hip', label: 'Hip', placeholder: 'e.g., 38 inches' },
        { field: 'shoulder', label: 'Shoulder Width', placeholder: 'e.g., 18 inches' },
        { field: 'sleeve', label: 'Sleeve Length', placeholder: 'e.g., 25 inches' },
        { field: 'jacket_length', label: 'Jacket Length', placeholder: 'e.g., 28 inches' },
        { field: 'trouser_waist', label: 'Trouser Waist', placeholder: 'e.g., 32 inches' },
        { field: 'trouser_length', label: 'Trouser Length', placeholder: 'e.g., 42 inches' },
        { field: 'inseam', label: 'Inseam', placeholder: 'e.g., 32 inches' }
      ],
      shirt: [
        { field: 'chest', label: 'Chest', placeholder: 'e.g., 38 inches' },
        { field: 'waist', label: 'Waist', placeholder: 'e.g., 32 inches' },
        { field: 'shoulder', label: 'Shoulder Width', placeholder: 'e.g., 16 inches' },
        { field: 'sleeve', label: 'Sleeve Length', placeholder: 'e.g., 24 inches' },
        { field: 'shirt_length', label: 'Shirt Length', placeholder: 'e.g., 28 inches' },
        { field: 'neck', label: 'Neck', placeholder: 'e.g., 15.5 inches' }
      ],
      trouser: [
        { field: 'waist', label: 'Waist', placeholder: 'e.g., 32 inches' },
        { field: 'hip', label: 'Hip', placeholder: 'e.g., 38 inches' },
        { field: 'thigh', label: 'Thigh', placeholder: 'e.g., 22 inches' },
        { field: 'trouser_length', label: 'Length', placeholder: 'e.g., 42 inches' },
        { field: 'inseam', label: 'Inseam', placeholder: 'e.g., 32 inches' }
      ]
    };

    // Determine category type
    if (categoryLower.includes('dress')) return measurementMap.dress;
    if (categoryLower.includes('suit') || categoryLower.includes('blazer')) return measurementMap.suit;
    if (categoryLower.includes('shirt') || categoryLower.includes('blouse') || categoryLower.includes('top')) return measurementMap.shirt;
    if (categoryLower.includes('trouser') || categoryLower.includes('pants') || categoryLower.includes('bottom')) return measurementMap.trouser;

    // Default measurements
    return [
      { field: 'chest', label: 'Chest/Bust', placeholder: 'e.g., 36 inches' },
      { field: 'waist', label: 'Waist', placeholder: 'e.g., 28 inches' },
      { field: 'hip', label: 'Hip', placeholder: 'e.g., 36 inches' },
      { field: 'length', label: 'Length', placeholder: 'e.g., 24 inches' }
    ];
  };

  const measurementFields = getMeasurementFields(product.category);

  // Initialize product delivery options
  useEffect(() => {
    console.log('Product data:', product); // Debug log
    console.log('Product delivery time options:', product.deliveryTimeOptions); // Debug log

    // Fetch actual delivery time options from the product
    const productDeliveryOpts = product.deliveryTimeOptions;

    if (productDeliveryOpts) {
      setProductDeliveryOptions({
        ...productDeliveryOpts,
        shopAddress: product.designer?.businessAddress || 'Designer shop address will be provided'
      });

      // Set default delivery type to the first enabled option
      const enabledOptions = Object.entries(productDeliveryOpts)
        .filter(([key, option]) => key !== 'shopAddress' && option?.enabled);
      if (enabledOptions.length > 0) {
        setFormData(prev => ({ ...prev, deliveryType: enabledOptions[0][0] }));
      }
    } else {
      // Fallback to default options if product doesn't have delivery options configured
      setProductDeliveryOptions({
        standard: { enabled: true, days: 14, description: 'Standard delivery', price: 0 },
        express: { enabled: false, days: 7, description: 'Express delivery', price: 0 },
        premium: { enabled: false, days: 3, description: 'Premium delivery', price: 0 },
        shopAddress: 'Designer shop address will be provided'
      });
    }
  }, [product]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleMeasurementChange = (field: string, value: string) => {
    // Update form data
    setFormData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [field]: value
      }
    }));

    // Validate the measurement
    const validation = validateMeasurement(field, value);

    // Update errors
    setMeasurementErrors(prev => {
      const newErrors = { ...prev };
      if (validation.isValid) {
        delete newErrors[field];
      } else if (validation.error) {
        newErrors[field] = validation.error;
      }
      return newErrors;
    });

    // Update warnings
    setMeasurementWarnings(prev => {
      const newWarnings = { ...prev };
      if (validation.warning) {
        newWarnings[field] = validation.warning;
      } else {
        delete newWarnings[field];
      }
      return newWarnings;
    });
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      addToast('error', 'Please log in to place a custom order');
      return;
    }

    // Validate required fields
    if (!formData.color.trim()) {
      addToast('error', 'Please specify your preferred color');
      return;
    }

    if (!formData.deliveryType) {
      addToast('error', 'Please select a delivery type');
      return;
    }

    // Check if the selected delivery type is available
    if (!productDeliveryOptions?.[formData.deliveryType]?.enabled) {
      addToast('error', 'Selected delivery type is not available for this product');
      return;
    }

    if (formData.collectionMethod === 'delivery' && !formData.deliveryLocation.trim()) {
      addToast('error', 'Please specify your delivery location');
      return;
    }

    // Check for measurement validation errors
    if (Object.keys(measurementErrors).length > 0) {
      addToast('error', 'Please fix the measurement errors before submitting');
      return;
    }

    // Check if at least some measurements are provided
    const providedMeasurements = Object.values(formData.measurements).filter(m => m && m.trim());
    if (providedMeasurements.length === 0) {
      addToast('error', 'Please provide at least some measurements');
      return;
    }

    try {
      setLoading(true);

      // Prepare custom order data
      const selectedDeliveryOption = productDeliveryOptions?.[formData.deliveryType];
      const deliveryDays = selectedDeliveryOption?.days || 14;
      const deliveryDescription = selectedDeliveryOption?.description || 'Standard delivery';
      const deliveryTimePrice = selectedDeliveryOption?.price || 0;

      // Calculate expected delivery date based on delivery type
      const expectedDeliveryDate = new Date();
      expectedDeliveryDate.setDate(expectedDeliveryDate.getDate() + deliveryDays);

      const customOrderData = {
        measurements: formData.measurements,
        expectedDeliveryDate: expectedDeliveryDate.toISOString().split('T')[0], // Auto-calculated based on delivery type
        deliveryType: formData.deliveryType,
        deliveryTimePrice: deliveryTimePrice, // Include delivery time price
        collectionMethod: formData.collectionMethod,
        deliveryLocation: formData.collectionMethod === 'delivery' ? formData.deliveryLocation : '',
        designerShopAddress: formData.collectionMethod === 'pickup' ? (productDeliveryOptions?.shopAddress || '') : '',
        additionalNotes: `${formData.additionalNotes}\n\nDelivery: ${deliveryDescription} (${deliveryDays} days) - MWK ${deliveryTimePrice.toLocaleString()}\nCollection: ${formData.collectionMethod === 'delivery' ? 'Delivery to location' : 'Pickup at designer shop'}`,
        productType: product.category || 'Fashion Item',
        estimatedPrice: product.price // Base price - delivery time price is separate
      };

      // Add custom order to cart
      addCustomOrderToCart(product as any, customOrderData, formData.color);

      addToast('success', 'Custom order added to cart! Complete payment to send request to designer.');
      onClose();

      // Reset form
      setFormData({
        color: '',
        deliveryType: 'standard',
        collectionMethod: 'delivery',
        deliveryLocation: '',
        additionalNotes: '',
        measurements: {}
      });


    } catch (error) {
      console.error('Error creating custom order:', error);
      addToast('error', 'Failed to send custom order request');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-3">
      <div className="bg-white shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden rounded-lg"
           style={{
             boxShadow: '0 0 30px rgba(234, 179, 8, 0.2), 0 8px 25px rgba(0, 0, 0, 0.3)'
           }}>
        {/* Header - Shorter and slicker */}
        <div className="bg-black text-white px-4 py-2 border-b border-yellow-400">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Package className="h-4 w-4 mr-2 text-yellow-400" />
              <h2 className="text-sm font-bold text-white">Custom Order Request</h2>
            </div>
            <button
              type="button"
              title="Close modal"
              onClick={onClose}
              className="text-gray-300 hover:text-yellow-400 transition-colors p-1"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row" style={{ height: 'calc(95vh - 60px)' }}>
          {/* Product Reference - Made wider to show full details */}
          <div className="lg:w-1/3 bg-gray-50 p-4 overflow-y-auto border-r border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
              <Package className="h-4 w-4 mr-2 text-yellow-600" />
              Product Reference
            </h3>

            {/* Product Image - 1:1 aspect ratio (square) */}
            <div className="mb-4">
              <img
                src={product.images[0]}
                alt={product.name}
                className="w-full aspect-square object-cover rounded-lg shadow-md border border-gray-200"
              />
            </div>

            {/* Product Info - More detailed */}
            <div className="space-y-3">
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                <h4 className="font-semibold text-gray-900 text-sm mb-1">{product.name}</h4>
                <p className="text-xs text-gray-600 mb-2 capitalize">{product.category} • Unisex • Top</p>

                <div className="flex items-center mb-2">
                  <User className="h-3 w-3 text-gray-400 mr-1" />
                  <span className="text-xs text-gray-600">by {product.designer.name}</span>
                </div>

                <div className="border-t border-gray-100 pt-2">
                  <p className="text-xs text-gray-500 mb-1">Base Price Estimate</p>
                  <p className="text-sm font-bold text-yellow-600">MWK {product.price.toLocaleString()}</p>
                  <p className="text-xs text-gray-500 italic">*Final price may vary based on customization</p>
                </div>
              </div>
            </div>
          </div>

          {/* Order Form */}
          <div className="lg:w-2/3 flex flex-col">
            <div className="flex-1 p-4 overflow-y-auto">
              {/* Important Disclaimer */}
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Important: Measurement Accuracy Required
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p className="mb-2">
                        <strong>Please ensure all measurements and details are accurate.</strong> Custom orders are made specifically to your measurements and cannot be returned or exchanged.
                      </p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Double-check all measurements before submitting</li>
                        <li>Provide measurements in the same unit (inches or centimeters)</li>
                        <li>Include any special requirements in the additional notes</li>
                        <li>Custom orders typically take longer than ready-made items</li>
                      </ul>
                      <p className="mt-2 font-medium">
                        By proceeding, you acknowledge that custom orders are final and non-refundable.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
              {/* Color Selection */}
              <div>
                <label className="flex items-center text-xs font-medium text-gray-700 mb-1">
                  <Palette className="h-3 w-3 mr-1" />
                  Preferred Color *
                </label>
                <input
                  type="text"
                  value={formData.color}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                  placeholder="e.g., Royal Blue, Emerald Green, Custom Pattern"
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500"
                  required
                />
              </div>

              {/* Measurements */}
              <div>
                <label className="flex items-center text-xs font-medium text-gray-700 mb-2">
                  <Ruler className="h-3 w-3 mr-1" />
                  Measurements (in inches)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {measurementFields.map((field) => {
                    const hasError = measurementErrors[field.field];
                    const hasWarning = measurementWarnings[field.field];

                    return (
                      <div key={field.field}>
                        <label className="block text-xs text-gray-600 mb-1">
                          {field.label}
                        </label>
                        <input
                          type="text"
                          value={formData.measurements[field.field] || ''}
                          onChange={(e) => handleMeasurementChange(field.field, e.target.value)}
                          placeholder="e.g., 36.5"
                          className={`w-full px-2 py-1.5 border rounded focus:ring-1 text-xs ${
                            hasError
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : hasWarning
                              ? 'border-yellow-500 focus:ring-yellow-500 focus:border-yellow-500'
                              : 'border-gray-300 focus:ring-yellow-500 focus:border-yellow-500'
                          }`}
                        />
                        {hasError && (
                          <p className="text-xs text-red-600 mt-1 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {hasError}
                          </p>
                        )}
                        {!hasError && hasWarning && (
                          <p className="text-xs text-yellow-600 mt-1 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {hasWarning}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-1">
                          {getMeasurementInfo(field.field)}
                        </p>
                      </div>
                    );
                  })}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  💡 Tip: Provide at least 3-4 key measurements for better fitting
                </p>
              </div>



              {/* Delivery Type */}
              <div>
                <label className="flex items-center text-xs font-medium text-gray-700 mb-1">
                  <Package className="h-3 w-3 mr-1" />
                  Delivery Type *
                </label>
                {productDeliveryOptions ? (
                  <>
                    <select
                      title="Select delivery type"
                      value={formData.deliveryType}
                      onChange={(e) => handleInputChange('deliveryType', e.target.value)}
                      className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500"
                      required
                    >
                      {productDeliveryOptions.standard?.enabled && (
                        <option value="standard">
                          Standard ({productDeliveryOptions.standard.days} days) - MWK {productDeliveryOptions.standard.price?.toLocaleString() || '0'}
                        </option>
                      )}
                      {productDeliveryOptions.express?.enabled && (
                        <option value="express">
                          Express ({productDeliveryOptions.express.days} days) - MWK {productDeliveryOptions.express.price?.toLocaleString() || '0'}
                        </option>
                      )}
                      {productDeliveryOptions.premium?.enabled && (
                        <option value="premium">
                          Premium ({productDeliveryOptions.premium.days} days) - MWK {productDeliveryOptions.premium.price?.toLocaleString() || '0'}
                        </option>
                      )}
                    </select>

                    {/* Show delivery timeline and price info for selected option */}
                    {formData.deliveryType && productDeliveryOptions[formData.deliveryType]?.enabled && (
                      <div className="mt-1 text-xs text-gray-600">
                        <span className="inline-flex items-center">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Estimated completion: {productDeliveryOptions[formData.deliveryType].days} days from order confirmation
                        </span>
                        <div className="mt-1 text-xs font-medium text-gray-800">
                          Delivery cost: MWK {productDeliveryOptions[formData.deliveryType].price?.toLocaleString() || '0'}
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md bg-gray-50 text-gray-500">
                    Loading delivery options...
                  </div>
                )}
              </div>

              {/* Collection Method */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Collection Method *
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <label className="flex items-center p-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 text-xs">
                    <input
                      type="radio"
                      name="collectionMethod"
                      value="delivery"
                      checked={formData.collectionMethod === 'delivery'}
                      onChange={(e) => handleInputChange('collectionMethod', e.target.value)}
                      className="mr-2"
                    />
                    <div>
                      <div className="font-medium">Delivery</div>
                      <div className="text-gray-500">To your location</div>
                    </div>
                  </label>
                  <label className="flex items-center p-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 text-xs">
                    <input
                      type="radio"
                      name="collectionMethod"
                      value="pickup"
                      checked={formData.collectionMethod === 'pickup'}
                      onChange={(e) => handleInputChange('collectionMethod', e.target.value)}
                      className="mr-2"
                    />
                    <div>
                      <div className="font-medium">Pickup</div>
                      <div className="text-gray-500">At designer shop</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Location Details */}
              <div>
                <label className="flex items-center text-xs font-medium text-gray-700 mb-1">
                  <MapPin className="h-3 w-3 mr-1" />
                  {formData.collectionMethod === 'delivery' ? 'Delivery Location *' : 'Designer Shop Address'}
                </label>
                {formData.collectionMethod === 'delivery' ? (
                  <input
                    type="text"
                    value={formData.deliveryLocation}
                    onChange={(e) => handleInputChange('deliveryLocation', e.target.value)}
                    placeholder="e.g., Lilongwe, Area 10"
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500"
                    required
                  />
                ) : (
                  <div className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                    {productDeliveryOptions?.shopAddress || 'Designer shop address will be provided'}
                  </div>
                )}
              </div>

              {/* Additional Notes */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Additional Notes
                </label>
                <textarea
                  value={formData.additionalNotes}
                  onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                  placeholder="Any special requests, style preferences, or additional details..."
                  rows={3}
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>



              {/* Submit Button */}
              <div className="flex justify-end space-x-2 pt-3 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-1.5 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-1.5 bg-black text-yellow-400 rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                  style={{
                    boxShadow: '0 0 10px rgba(234, 179, 8, 0.3)'
                  }}
                >
                  {loading ? 'Adding to Cart...' : 'Add Custom Order to Cart'}
                </button>
              </div>
            </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCustomOrder;

import React, { useState } from 'react';
import { Palette, Sparkles } from 'lucide-react';
import CustomOrderModal from './CustomOrderModal';

interface FloatingCustomOrderButtonProps {
  className?: string;
}

const FloatingCustomOrderButton: React.FC<FloatingCustomOrderButtonProps> = ({
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCustomOrderClick = () => {
    console.log('🎨 Custom order button clicked');
    setIsModalOpen(true);
  };

  return (
    <>
      {/* Floating Custom Order Button */}
      <div className={`fixed bottom-16 right-4 z-40 group ${className}`}>
        <button
          onClick={handleCustomOrderClick}
          data-custom-order-button
          className="relative bg-black hover:bg-gray-800 text-yellow-400 p-3 rounded-xl shadow-lg hover:shadow-2xl transform hover:scale-110 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-yellow-500/50 border-2 border-yellow-400/30"
          aria-label="Create custom order"
          title="Create Custom Order"
          style={{
            boxShadow: '0 0 20px rgba(234, 179, 8, 0.3), 0 4px 15px rgba(0, 0, 0, 0.3)'
          }}
        >
          {/* Main icon with sparkle effect */}
          <div className="relative">
            <Palette className="h-5 w-5" />
            <Sparkles className="h-2.5 w-2.5 absolute -top-1 -right-1 text-yellow-400 animate-pulse" />
          </div>

          {/* Rotating border animation */}
          <div className="absolute inset-0 rounded-xl bg-yellow-400 animate-spin opacity-20" style={{ animationDuration: '3s' }}></div>

          {/* Pulse animation */}
          <div className="absolute inset-0 rounded-xl bg-yellow-400 animate-ping opacity-10"></div>

          {/* Enhanced Tooltip - positioned to the left */}
          <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 px-3 py-1.5 bg-black text-yellow-400 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap border border-yellow-400/30"
               style={{
                 boxShadow: '0 0 15px rgba(234, 179, 8, 0.2), 0 2px 10px rgba(0, 0, 0, 0.3)'
               }}>
            <div className="flex items-center space-x-1.5">
              <Palette className="h-3 w-3 text-yellow-400" />
              <span className="font-medium">Create Custom Design</span>
            </div>
            {/* Arrow pointing right */}
            <div className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-r-0 border-t-4 border-b-4 border-transparent border-l-black"></div>
          </div>
        </button>
      </div>

      {/* Custom Order Modal */}
      <CustomOrderModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};

export default FloatingCustomOrderButton;
